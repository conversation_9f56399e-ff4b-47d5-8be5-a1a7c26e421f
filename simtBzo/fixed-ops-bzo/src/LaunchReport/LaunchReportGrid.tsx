import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  Typography,
  CircularProgress,
  Snackbar,
  Alert,
} from "@mui/material";
import CheckBoxOutlineBlankIcon from "@mui/icons-material/CheckBoxOutlineBlank";
import CheckBoxIcon from "@mui/icons-material/CheckBox";
import { useSidebarState } from "react-admin";
import {
  GetLaunchReportsListDetails,
  GetAduOpenStoreWorkpackages,
} from "../service/launchReportsList";
import { UserData } from "../types";
import ExampleComponent from "./HandsonTable";

interface MenuData {
  onboardingTotal?: number;
  needsObMeeting?: number;
  dmsNotActive?: number;
  opsNotCategorized?: number;
  gridNotEntered?: number;
  modelsNotMapped?: number;
  matrixNotEntered?: number;
  usersNotCreated?: number;
  launchCompleted?: number;
  activeBilling?: number;
  trials?: number;
  trialConversions?: number;
  thirdPartyProviders?: number;
  cancellations?: number;
  preLaunchCancellations?: number;
  postLaunchCancellations?: number;
  trialCancellations?: number;
}

const LaunchReportGrid = () => {
  const [sidebarIsOpen] = useSidebarState();
  const [allMenu, setAllMenu] = useState<MenuData>({}); // Changed to MenuData type
  const [selectedKey, setSelectedKey] = useState("onboardingTotal");
  const [tableData, setTableData] = useState<UserData[]>([]);
  const [allTypeValue, setAllTypeValue] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    fetchLaunchReportData();
  }, [refreshTrigger]);

  useEffect(() => {
    fetchTableData();
  }, [selectedKey, refreshTrigger]);

  const handleRefreshTrigger = () => {
    setRefreshTrigger((prev) => !prev);
  };

  const fetchLaunchReportData = async () => {
    try {
      const res = await GetLaunchReportsListDetails();
      if (res && Array.isArray(res) && res[0]) {
        setAllMenu(res[0]);
      }
    } catch (error) {
      console.error(error);
      setErrorMessage("⚠️ Failed to load dashboard data.");
    }
  };

  const fetchTableData = async () => {
    setLoading(true);
    try {
      const res = await GetAduOpenStoreWorkpackages();
      if (!Array.isArray(res)) throw new Error("API did not return array");
      setAllTypeValue(res as UserData[]);

      let filtered: UserData[] = [];
      switch (selectedKey) {
        case "onboardingTotal":
          filtered = res.filter((i: any) => i.launchCompleted == null);
          break;
        case "launchCompleted":
          filtered = res.filter((i: any) => i.launchCompleted != null);
          break;
        case "cancellations":
          filtered = res.filter((i: any) => i.cancellationDate != null);
          break;
        default:
          filtered = res as UserData[];
      }

      setTableData(filtered);
    } catch (error) {
      console.error("fetchTableData error:", error);
      setErrorMessage(
        "⚠️ Failed to fetch launch progress report data. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const cardData = [
    {
      key: "onboardingTotal",
      title: `Onboarding Total : ${allMenu.onboardingTotal || 0}`,
      color: "#2196F3",
      items: [
        { label: "Needs OB Meeting", value: allMenu.needsObMeeting },
        { label: "DMS Not Active", value: allMenu.dmsNotActive },
        { label: "Ops Not Categorized", value: allMenu.opsNotCategorized },
        { label: "Grid Not Entered", value: allMenu.gridNotEntered },
        { label: "Models Not Mapped", value: allMenu.modelsNotMapped },
        { label: "Matrix Not Entered", value: allMenu.matrixNotEntered },
        { label: "Users Not Created", value: allMenu.usersNotCreated },
      ],
    },
    {
      key: "launchCompleted",
      title: `Launch Completed : ${allMenu.launchCompleted || 0}`,
      color: "#F57C00",
      items: [
        { label: "Active Billing", value: allMenu.activeBilling },
        { label: "Trials", value: allMenu.trials },
        { label: "Trial Conversions", value: allMenu.trialConversions },
        { label: "3PP", value: allMenu.thirdPartyProviders },
      ],
    },
    {
      key: "cancellations",
      title: `Cancellations : ${allMenu.cancellations || 0}`,
      color: "#C62828",
      items: [
        {
          label: "Pre-Launch Cancellations",
          value: allMenu.preLaunchCancellations,
        },
        {
          label: "Post-Launch Cancellations",
          value: allMenu.postLaunchCancellations,
        },
        { label: "Trial Cancellations", value: allMenu.trialCancellations },
      ],
    },
  ];

  return (
    <Box
      sx={{
        paddingX: "10px",
        mt: "15px",
        width: "100%",
        "@media (max-width: 1440px)": { width: "83vw" },
      }}>
      {/* ====== Summary Cards ====== */}
      <Grid container spacing={2} width="100%">
        {cardData.map((card) => (
          <Grid item xs={12} md={4} key={card.key}>
            <Box
              onClick={() => !loading && setSelectedKey(card.key)}
              sx={{
                backgroundColor: card.color,
                color: "white",
                borderRadius: 2,
                p: 2,
                boxShadow: 3,
                cursor: "pointer",
                minHeight: 230,
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                "&:hover": {
                  transform: "scale(1.02)",
                  transition: "transform 0.2s ease-in-out",
                },
              }}>
              {/* Header */}
              <Box
                display="flex"
                alignItems="center"
                mb={0.5} // 🔹 reduced gap
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedKey(card.key);
                }}>
                {selectedKey === card.key ? (
                  <CheckBoxIcon
                    sx={{ color: "white", mr: 1, cursor: "pointer" }}
                  />
                ) : (
                  <CheckBoxOutlineBlankIcon
                    sx={{ color: "white", mr: 1, cursor: "pointer" }}
                  />
                )}
                <Typography variant="h6" fontWeight="bold">
                  {card.title}
                </Typography>
              </Box>

              {/* Items with Checkboxes */}
              <Grid
                container
                spacing={0.5} // 🔹 tighter spacing
                columns={card.key === "onboardingTotal" ? 2 : 1}
                sx={{ pl: 2, mt: 0 }} // 🔹 reduced left padding and top margin
              >
                {card.items.map((item, idx) => (
                  <Grid item xs={1} key={idx}>
                    <Box
                      display="flex"
                      alignItems="center"
                      onClick={(e) => {
                        e.stopPropagation();
                        item.checked = !item.checked; // toggle check
                        setCardData([...cardData]); // trigger re-render
                      }}
                      sx={{ cursor: "pointer" }}>
                      {item.checked ? (
                        <CheckBoxIcon
                          sx={{ color: "white", fontSize: 16, mr: 0.5 }}
                        />
                      ) : (
                        <CheckBoxOutlineBlankIcon
                          sx={{ color: "white", fontSize: 16, mr: 0.5 }}
                        />
                      )}
                      <Typography variant="body2">
                        {item.label}: {item.value || 0}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Grid>
        ))}
      </Grid>

      {/* ====== Handsontable Section ====== */}
      {selectedKey && (
        <Box sx={{ mt: 2, width: "100%" }}>
          <div
            id="hand-table"
            className={sidebarIsOpen ? "sidebar-open" : "sidebar-closed"}
            style={{ width: "80vw", marginBottom: 0, paddingBottom: 0 }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height={200}>
                <CircularProgress />
              </Box>
            ) : (
              <ExampleComponent
                tableData={tableData}
                selectedKey={selectedKey}
                allTypeValue={allTypeValue}
                refreshTrigger={handleRefreshTrigger}
              />
            )}
          </div>
        </Box>
      )}

      {/* ====== Snackbar for Errors ====== */}
      <Snackbar
        open={!!errorMessage}
        autoHideDuration={8000}
        onClose={() => setErrorMessage("")}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}>
        <Alert
          onClose={() => setErrorMessage("")}
          severity="error"
          sx={{ width: "100%" }}>
          {errorMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default LaunchReportGrid;
